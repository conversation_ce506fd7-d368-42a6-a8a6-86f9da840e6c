<template>
	<page-container ref="pageContainer" :is-back="true" title="Nginx环境管理">
		<view class="nginx-env-container">
			<!-- 服务状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="status-info">
						<text class="status-title">Nginx服务</text>
						<text class="status-desc">当前运行状态</text>
					</view>
					<view class="status-control">
						<text class="status-badge" :class="serviceStatus.isRunning ? 'badge-success' : 'badge-danger'">
							{{ serviceStatus.isRunning ? '运行中' : '已停止' }}
						</text>
						<uv-switch
							:model-value="serviceStatus.isRunning"
							size="24"
							activeColor="#20a50a"
							:loading="serviceLoading"
							@change="toggleService"
						></uv-switch>
					</view>
				</view>

				<!-- 快速操作按钮 -->
				<view class="quick-actions">
					<view
						v-for="(action, index) in quickActions"
						:key="index"
						class="action-btn"
						:class="{
							'start-btn': action.id === 'start',
							'stop-btn': action.id === 'stop'
						}"
						@tap="handleQuickAction(action)"
						hover-class="action-hover"
					>
						<uv-icon
							:name="action.icon"
							size="24"
							:color="action.id === 'stop' ? '#dc2626' : '#20a50a'"
						></uv-icon>
						<text class="action-text">{{ action.title }}</text>
					</view>
				</view>
			</view>

			<!-- 功能模块 -->
			<view class="function-modules">
				<view
					v-for="(module, index) in functionModules"
					:key="index"
					class="module-card"
					:class="{ 'expanded': module.id === 'status' && isLoadStatusExpanded }"
				>
					<view
						class="module-header"
						@tap="handleModuleClick(module)"
					>
						<view class="module-icon">
							<uv-icon :name="module.icon" size="24" color="#20a50a"></uv-icon>
						</view>
						<text class="module-title">{{ module.title }}</text>
						<view v-if="module.id === 'status'" class="expand-icon">
							<uv-icon
								:name="isLoadStatusExpanded ? 'arrow-up' : 'arrow-down'"
								size="16"
								color="var(--text-secondary-color)"
							></uv-icon>
						</view>
					</view>
					<view class="module-desc">{{ module.desc }}</view>
					<view
						v-if="module.showData"
						class="module-data"
						:class="{ 'clickable': module.id === 'version' }"
						@tap="module.id === 'version' ? handleModuleClick(module) : null"
					>
						<view class="data-content">
							<text class="data-value">{{ module.dataValue }}</text>
							<text class="data-unit">{{ module.dataUnit }}</text>
						</view>
						<view v-if="module.id === 'version'" class="dropdown-icon">
							<uv-icon name="arrow-down" size="16" color="var(--text-secondary-color)"></uv-icon>
						</view>
					</view>

					<!-- 负载状态详细信息 -->
					<view v-if="module.id === 'status' && isLoadStatusExpanded" class="load-details">
						<view class="load-stats-grid">
							<!-- 连接统计 -->
							<view class="stats-section">
								<text class="section-title">连接统计</text>
								<view class="stats-row">
									<view class="stat-item">
										<text class="stat-label">活动连接数</text>
										<text class="stat-value primary">{{ loadStats.activeConnections }}</text>
									</view>
									<view class="stat-item">
										<text class="stat-label">总连接数</text>
										<text class="stat-value">{{ loadStats.accepts }}</text>
									</view>
								</view>
								<view class="stats-row">
									<view class="stat-item">
										<text class="stat-label">总握手数</text>
										<text class="stat-value">{{ loadStats.handled }}</text>
									</view>
									<view class="stat-item">
										<text class="stat-label">总请求数</text>
										<text class="stat-value">{{ loadStats.requests }}</text>
									</view>
								</view>
							</view>

							<!-- 状态统计 -->
							<view class="stats-section">
								<text class="section-title">状态统计</text>
								<view class="stats-row">
									<view class="stat-item">
										<text class="stat-label">读取数</text>
										<text class="stat-value">{{ loadStats.reading }}</text>
									</view>
									<view class="stat-item">
										<text class="stat-label">响应数</text>
										<text class="stat-value">{{ loadStats.writing }}</text>
									</view>
								</view>
								<view class="stats-row">
									<view class="stat-item">
										<text class="stat-label">等待数</text>
										<text class="stat-value">{{ loadStats.waiting }}</text>
									</view>
									<view class="stat-item">
										<text class="stat-label">工作进程</text>
										<text class="stat-value">{{ loadStats.worker }}</text>
									</view>
								</view>
							</view>

							<!-- 资源占用 -->
							<view class="stats-section">
								<text class="section-title">资源占用</text>
								<view class="stats-row">
									<view class="stat-item">
										<text class="stat-label">CPU占用</text>
										<text class="stat-value">{{ loadStats.workerCpu }}%</text>
									</view>
									<view class="stat-item">
										<text class="stat-label">内存占用</text>
										<text class="stat-value">{{ loadStats.workerMem }}</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 刷新按钮 -->
						<view class="load-actions">
							<view
								class="refresh-btn"
								@tap="refreshLoadStatus"
								hover-class="refresh-btn-hover"
							>
								<uv-icon
									name="reload"
									size="16"
									color="#20a50a"
									:class="{ 'rotating': loadStatusLoading }"
								></uv-icon>
								<text class="refresh-text">刷新数据</text>
							</view>
						</view>
					</view>
				</view>
			</view>


		</view>

		<!-- 版本选择器 -->
		<uv-picker
			ref="versionPicker"
			:columns="versionColumns"
			title="选择Nginx版本"
			@confirm="onVersionConfirm"
			@cancel="onVersionCancel"
		></uv-picker>

	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onShow } from '@dcloudio/uni-app';
	import { ref } from 'vue';
	import {
		pageContainer,
		serviceStatus,
		serviceLoading,
		functionModules,
		quickActions,
		toggleService,
		handleModuleClick,
		handleQuickAction,
		initNginxEnvData,
		// 负载状态相关
		isLoadStatusExpanded,
		loadStats,
		loadStatusLoading,
		refreshLoadStatus,
		// 版本选择器相关
		versionColumns,
		onVersionConfirm,
		onVersionCancel,
		setVersionPickerRef
	} from './useController.js';

	// 创建模板 ref
	const versionPicker = ref(null);

	onShow(async () => {
		await initNginxEnvData();
		// 将模板 ref 传递给 useController
		setVersionPickerRef(versionPicker);
	});
</script>

<style lang="scss" scoped>
	.nginx-env-container {
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		background-color: var(--page-bg-color);
	}

	/* 服务状态卡片 */
	.status-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 32rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid rgba(32, 165, 10, 0.1);
	}

	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.status-info {
		.status-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			display: block;
		}

		.status-desc {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-top: 8rpx;
		}
	}

	.status-control {
		display: flex;
		align-items: center;
		gap: 24rpx;
	}

	.status-badge {
		font-size: 24rpx;
		font-weight: 500;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;

		&.badge-success {
			background-color: #dcfce7;
			color: #16a34a;
		}

		&.badge-danger {
			background-color: #fee2e2;
			color: #dc2626;
		}
	}

	/* 快速操作按钮 */
	.quick-actions {
		display: flex;
		justify-content: space-around;
		gap: 24rpx;
		margin-top: 24rpx;
		padding-top: 24rpx;
		border-top: 2rpx solid var(--border-color);
	}

	.action-btn {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding: 20rpx 16rpx;
		border-radius: 12rpx;
		background-color: transparent;
		border: 2rpx solid rgba(32, 165, 10, 0.3);
		transition: all 0.2s ease;
		flex: 1;
		gap: 12rpx;

		.action-text {
			font-size: 24rpx;
			color: var(--text-primary-color);
			font-weight: 500;
		}
	}

	.action-hover {
		background-color: rgba(32, 165, 10, 0.1);
		border-color: rgba(32, 165, 10, 0.6);
		transform: translateY(-2rpx);
	}

	/* 启动按钮特殊样式 */
	.action-btn.start-btn {
		flex: none;
		width: 200rpx;
		margin: 0 auto;
		border-color: rgba(32, 165, 10, 0.5);
		background-color: rgba(32, 165, 10, 0.05);
	}

	/* 停止按钮特殊样式 */
	.action-btn.stop-btn {
		border-color: rgba(220, 38, 38, 0.3);
		background-color: rgba(220, 38, 38, 0.05);
	}

	.action-btn.stop-btn.action-hover {
		border-color: rgba(220, 38, 38, 0.6);
		background-color: rgba(220, 38, 38, 0.1);
	}

	/* 功能模块 */
	.function-modules {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.module-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 28rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid rgba(0, 0, 0, 0.05);
	}

	.module-header {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 16rpx;
		cursor: pointer;

		.module-icon {
			width: 52rpx;
			height: 52rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.15) 0%, rgba(32, 165, 10, 0.05) 100%);
			border-radius: 16rpx;
			border: 2rpx solid rgba(32, 165, 10, 0.1);
		}

		.module-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			flex: 1;
		}

		.expand-icon {
			margin-left: auto;
			transition: transform 0.3s ease;
		}
	}

	.module-desc {
		font-size: 26rpx;
		color: var(--text-secondary-color);
		margin-bottom: 20rpx;
		line-height: 1.5;
	}

	.module-data {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		border-radius: 12rpx;
		border: 2rpx solid rgba(32, 165, 10, 0.1);

		&.clickable {
			cursor: pointer;
			transition: all 0.2s ease;

			&:hover {
				background: linear-gradient(135deg, rgba(32, 165, 10, 0.08) 0%, rgba(32, 165, 10, 0.04) 100%);
				border-color: rgba(32, 165, 10, 0.2);
			}
		}

		.data-content {
			display: flex;
			align-items: baseline;
			gap: 8rpx;
		}

		.data-value {
			font-size: 36rpx;
			font-weight: 700;
			color: #20a50a;
		}

		.data-unit {
			font-size: 24rpx;
			color: var(--text-secondary-color);
		}

		.dropdown-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s ease;
		}

		&.clickable:hover .dropdown-icon {
			transform: scale(1.1);
		}
	}

	/* 负载状态详细信息 */
	.module-card.expanded {
		border-color: rgba(32, 165, 10, 0.3);
	}

	.load-details {
		margin-top: 24rpx;
		padding-top: 24rpx;
		border-top: 2rpx solid var(--border-color);
		animation: slideDown 0.3s ease-out;
	}

	.load-stats-grid {
		display: flex;
		flex-direction: column;
		gap: 32rpx;
	}

	.stats-section {
		.section-title {
			font-size: 28rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			margin-bottom: 20rpx;
			padding-bottom: 12rpx;
			border-bottom: 2rpx solid rgba(32, 165, 10, 0.1);
		}
	}

	.stats-row {
		display: flex;
		gap: 16rpx;
		margin-bottom: 16rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 16rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		border-radius: 12rpx;
		border: 2rpx solid rgba(32, 165, 10, 0.1);
		box-sizing: border-box;
	}

	.stat-label {
		font-size: 22rpx;
		color: var(--text-secondary-color);
		margin-bottom: 8rpx;
		text-align: center;
	}

	.stat-value {
		font-size: 28rpx;
		font-weight: 700;
		color: var(--text-primary-color);
		text-align: center;

		&.primary {
			color: #20a50a;
			font-size: 32rpx;
		}
	}

	.load-actions {
		display: flex;
		justify-content: center;
		margin-top: 32rpx;
		padding-top: 24rpx;
		border-top: 2rpx solid var(--border-color);
	}

	.refresh-btn {
		display: flex;
		align-items: center;
		gap: 12rpx;
		padding: 16rpx 32rpx;
		background-color: transparent;
		border: 2rpx solid rgba(32, 165, 10, 0.3);
		border-radius: 12rpx;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.refresh-btn-hover {
		background-color: rgba(32, 165, 10, 0.1);
		border-color: rgba(32, 165, 10, 0.6);
		transform: translateY(-2rpx);
	}

	.refresh-text {
		font-size: 24rpx;
		color: var(--text-primary-color);
		font-weight: 500;
	}	
</style>